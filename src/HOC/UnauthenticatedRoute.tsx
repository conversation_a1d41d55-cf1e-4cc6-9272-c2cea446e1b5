import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Navigate, useLocation } from 'react-router-dom';
// services
import { localStorageService } from 'services/LocalStorageService';
// actions
import { authFetchMeAction } from 'store/actions/auth.action';
// selectors
import {
  isAuthLoadingSelector,
  isAuthenticatedSelector,
} from 'store/selectors/auth.selector';
// components
import { toast } from 'react-toastify';
import { UserRouteType } from 'types/enum';
import { getDefaultRoute, getUserRouteType } from 'utils/helpers/Helpers';

const UnauthenticatedRouteHOC = <P extends {}>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  const UnauthenticatedRoute: React.FC<P> = ({ ...props }) => {
    const dispatch = useDispatch();
    const location = useLocation();

    const isAuthenticated = useSelector(isAuthenticatedSelector);
    const isLoading = useSelector(isAuthLoadingSelector);
    const isAuthLoading = useSelector(isAuthLoadingSelector);

    const path = location.pathname.split('/')[1] ?? UserRouteType.ADVISOR;
    const loggedInUserType = useMemo(
      () => localStorageService.getLoggedInUserType(),
      []
    );

    useEffect(() => {
      const token = localStorageService.getAuthToken();

      if (token && !isAuthenticated && !isLoading) {
        dispatch(
          authFetchMeAction({
            userType: loggedInUserType
              ? getUserRouteType(loggedInUserType)
              : (path as UserRouteType),
          })
        );
      }
    }, [dispatch, isAuthenticated, isLoading]);

    useEffect(() => {
      if (!isAuthLoading) {
        if (isAuthenticated && !loggedInUserType) {
          toast.success('Login Successfull!');
        }
      }
    }, [isAuthLoading, isAuthenticated]);

    return !isAuthenticated && !loggedInUserType ? (
      <Component {...(props as P)} />
    ) : (
      <Navigate to={getDefaultRoute(loggedInUserType)} />
    );
  };

  return UnauthenticatedRoute;
};

export default UnauthenticatedRouteHOC;
