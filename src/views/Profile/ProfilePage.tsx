import React, { FC, memo, useMemo, useState } from 'react';
import { AiFillInfoCircle, AiFillLock } from 'react-icons/ai';
import { BiSolidKey } from 'react-icons/bi';
import { BsBuildingFill } from 'react-icons/bs';
import { FaMoneyBill } from 'react-icons/fa';
import { useSelector } from 'react-redux';
import BillingPage from 'views/AdvisorSubscription/BillingPage';
import ChangePassword from 'views/Profile/ChangePassword';
import GeneralProfile from 'views/Profile/GeneralProfile';
import { localStorageService } from '../../services/LocalStorageService';
import Modal from '../../shared-resources/components/Modal/Modal';
import { getUserData } from '../../store/selectors/user.selector';
import { ProfileSections, UserType } from '../../types/enum';
import CompanyDetailsComponent from './CompanyDetailsComponent';
import EditProfileDialog from './EditProfileDialog';
import ProfileTabs from './ProfileTabs';

interface Props {}

const ProfilePage: FC<Props> = () => {
  const user = useSelector(getUserData);
  const loggedInUserType = useMemo(
    () => localStorageService.getLoggedInUserType(),
    []
  );
  const tabs = useMemo(
    () =>
      loggedInUserType === UserType.ADVISOR
        ? [
            {
              title: ProfileSections.GENERAL,
              icon: <AiFillInfoCircle />,
            },
            { title: ProfileSections.PASSWORD, icon: <BiSolidKey /> },
            { title: ProfileSections.MFA, icon: <AiFillLock />, }, // Updated icon for MFA Setup
            { title: ProfileSections.BILLING, icon: <FaMoneyBill /> },
          ]
        : [
            {
              title: ProfileSections.GENERAL,
              icon: <AiFillInfoCircle />,
            },
            {
              title: ProfileSections.COMPANY_DETAILS,
              icon: <BsBuildingFill />,
            },
            { title: ProfileSections.PASSWORD, icon: <BiSolidKey /> },
            { title: ProfileSections.MFA, icon: <AiFillLock />, },
          ],
    [loggedInUserType]
  );
  const [selectedTab, setSelectedTab] = useState<string>(
    ProfileSections.GENERAL
  );
  const [showDialog, setShowDialog] = useState(false);

  return (
    <>
      <div>
        <h1 className='text-2xl font-semibold'>
          {loggedInUserType === UserType.ADVISOR
            ? 'Advisor Profile'
            : 'Business Owner Profile'}
        </h1>
        <ProfileTabs
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          tabs={tabs}
        />
      </div>
      {selectedTab === ProfileSections.GENERAL && (
        <GeneralProfile
          user={user}
          onEdit={() => {
            setShowDialog(true);
          }}
        />
      )}
      {selectedTab === ProfileSections.COMPANY_DETAILS && (
        <CompanyDetailsComponent />
      )}
      {selectedTab === ProfileSections.PASSWORD && <ChangePassword />}
      {selectedTab === ProfileSections.BILLING && <BillingPage />}
      {selectedTab === ProfileSections.MFA && <MFASetup />}
      <Modal
        visible={showDialog}
        title='Edit Profile'
        handleVisibility={setShowDialog}
        closeOnOutsideClick
        classname='max-w-[46rem]'
      >
        <EditProfileDialog
          user={user}
          onClose={() => {
            setShowDialog(false);
          }}
        />
      </Modal>
    </>
  );
};
export default memo(ProfilePage);
