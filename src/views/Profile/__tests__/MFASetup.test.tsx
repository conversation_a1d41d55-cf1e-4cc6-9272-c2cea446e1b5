import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import MFASetup from '../MFASetup';
import userReducer from 'store/reducers/user.reducer';
import { UserType } from 'types/enum';

// Mock the toast
jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock the user service
jest.mock('services/api-services/UserService', () => ({
  userService: {
    setupMFA: jest.fn(),
  },
}));

const mockUser = {
  id: 1,
  email: '<EMAIL>',
  first_name: 'Test',
  name: 'Test User',
  type: UserType.ADVISOR,
  mfaType: undefined,
};

const createMockStore = (user = mockUser) => {
  return configureStore({
    reducer: {
      user: userReducer,
    },
    preloadedState: {
      user: {
        ids: [1],
        entities: {
          1: user,
        },
        loading: false,
        error: '',
        errors: [],
        update: { loading: false },
        advisorSubscriptionData: {},
      },
    },
  });
};

describe('MFASetup Component', () => {
  it('renders MFA setup form with radio options', () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <MFASetup />
      </Provider>
    );

    expect(screen.getByText('Multi-Factor Authentication Setup')).toBeInTheDocument();
    expect(screen.getByText('Email - Receive verification codes via email')).toBeInTheDocument();
    expect(screen.getByText('Google Authenticator (TOTP) - Use an authenticator app for time-based codes')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /setup mfa/i })).toBeInTheDocument();
  });

  it('shows current MFA method when user has one set', () => {
    const userWithMFA = { ...mockUser, mfaType: 'email' as const };
    const store = createMockStore(userWithMFA);
    
    render(
      <Provider store={store}>
        <MFASetup />
      </Provider>
    );

    expect(screen.getByText('Current MFA Method:')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
  });

  it('allows user to select MFA preference and submit', async () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <MFASetup />
      </Provider>
    );

    // Select TOTP option
    const totpRadio = screen.getByDisplayValue('totp');
    fireEvent.click(totpRadio);

    // Submit form
    const submitButton = screen.getByRole('button', { name: /setup mfa/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(submitButton).toBeDisabled();
    });
  });

  it('defaults to email option when no MFA is set', () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <MFASetup />
      </Provider>
    );

    const emailRadio = screen.getByDisplayValue('email');
    expect(emailRadio).toBeChecked();
  });

  it('defaults to current MFA type when user has MFA set', () => {
    const userWithMFA = { ...mockUser, mfaType: 'totp' as const };
    const store = createMockStore(userWithMFA);
    
    render(
      <Provider store={store}>
        <MFASetup />
      </Provider>
    );

    const totpRadio = screen.getByDisplayValue('totp');
    expect(totpRadio).toBeChecked();
  });
});
