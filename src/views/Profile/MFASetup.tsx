import { FC, memo, useMemo, useState } from 'react';
import { Form, Formik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import * as yup from 'yup';
import Button from 'shared-resources/components/Button/Button';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { RadioItem } from 'shared-resources/types/Radio.type';
import { getUserData, getMFASetupLoading } from 'store/selectors/user.selector';
import { setupMFAAction, verifyMFASetupAction } from 'store/actions/user.action';
import { UserType } from 'types/enum';
import Modal from 'shared-resources/components/Modal/Modal';

interface MFASetupFormValues {
  mfa_preference: 'email' | 'totp';
}

interface QRCodeData {
  qr_code: string;
  secret: string;
}

interface VerificationFormValues {
  verification_code: string;
}

const mfaSetupSchema = yup.object().shape({
  mfa_preference: yup
    .string()
    .oneOf(['email', 'totp'], 'Please select a valid MFA option')
    .required('Please select an MFA option'),
});

const verificationSchema = yup.object().shape({
  verification_code: yup
    .string()
    .required('Verification code is required')
    .matches(/^\d{6}$/, 'Must be exactly 6 digits'),
});

const MFASetup: FC = () => {
  const dispatch = useDispatch();
  const user = useSelector(getUserData);
  const isLoading = useSelector(getMFASetupLoading);
  const [qrCodeData, setQrCodeData] = useState<QRCodeData | null>(null);
  const [showQRModal, setShowQRModal] = useState(false);
  const [showVerificationStep, setShowVerificationStep] = useState(false);
  const [verificationLoading, setVerificationLoading] = useState(false);

  // Debug logging
  console.log('MFA Setup - isLoading from selector:', isLoading);

  const mfaOptions: RadioItem[] = useMemo(
    () => [
      {
        label: 'Email - Receive verification codes via email',
        value: 'email',
      },
      {
        label: 'Google Authenticator (TOTP) - Use an authenticator app for time-based codes',
        value: 'totp',
      },
    ],
    []
  );

  const initialValues: MFASetupFormValues = {
    mfa_preference: user?.mfa_preference || 'email',
  };

  const handleSubmit = async (values: MFASetupFormValues, { setSubmitting }: any) => {
    if (!user?.email || !user?.type) {
      console.error('User email or type is missing');
      setSubmitting(false);
      return;
    }

    try {
      await new Promise<void>((resolve, reject) => {
        dispatch(
          setupMFAAction({
            email: user.email,
            mfa_preference: values.mfa_preference,
            user_type: user.type as UserType,
            onSuccess: (message: string, data?: any) => {
              console.log('MFA setup successful:', message, data);

              // If TOTP and response contains QR code data, show QR modal for scanning
              if (values.mfa_preference === 'totp' && data?.qr_code && data?.secret) {
                setQrCodeData({
                  qr_code: data.qr_code,
                  secret: data.secret,
                });
                setShowQRModal(true);
                setShowVerificationStep(false); // Start with QR code display
              }

              setSubmitting(false);
              resolve();
            },
            onError: (error: string) => {
              console.error('MFA setup failed:', error);
              setSubmitting(false);
              reject(new Error(error));
            },
          })
        );
      });
    } catch (error) {
      console.error('MFA setup error:', error);
      setSubmitting(false);
    }
  };

  const handleVerification = async (values: VerificationFormValues, { setSubmitting }: any) => {
    if (!user?.email || !user?.type || !qrCodeData?.secret) {
      console.error('Missing required data for verification');
      setSubmitting(false);
      return;
    }

    try {
      setVerificationLoading(true);
      await new Promise<void>((resolve, reject) => {
        dispatch(
          verifyMFASetupAction({
            email: user.email,
            verification_code: values.verification_code,
            user_type: user.type as UserType,
            secret: qrCodeData.secret,
            onSuccess: (message: string) => {
              console.log('MFA verification successful:', message);
              setVerificationLoading(false);
              setSubmitting(false);
              setShowQRModal(false);
              setShowVerificationStep(false);
              setQrCodeData(null);
              resolve();
            },
            onError: (error: string) => {
              console.error('MFA verification failed:', error);
              setVerificationLoading(false);
              setSubmitting(false);
              reject(new Error(error));
            },
          })
        );
      });
    } catch (error) {
      console.error('MFA verification error:', error);
      setVerificationLoading(false);
      setSubmitting(false);
    }
  };

  return (
    <div className='bg-white p-6 rounded-lg shadow-sm'>
      <div className='mb-6'>
        <h2 className='text-xl font-semibold text-gray-900 mb-2'>
          Multi-Factor Authentication Setup
        </h2>
        <p className='text-gray-600'>
          Choose your preferred method for multi-factor authentication to enhance your account security.
        </p>
      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={mfaSetupSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {() => {
          const isButtonLoading = isLoading;
          return (
            <Form className='space-y-6'>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-3'>
                  Select MFA Method
                </label>
                <FormikRadio
                  name='mfa_preference'
                  options={mfaOptions}
                  className2='p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors'
                  inputClassName='text-blue-600 focus:ring-blue-500'
                  labelClassName='text-gray-900 font-medium'
                />
              </div>

              <div className='pt-4'>
                <Button
                  type='submit'
                  isSubmitting={isButtonLoading}
                  disabled={isButtonLoading}
                  className='px-6 py-2'
                >
                  {isButtonLoading ? 'Setting up...' : 'Setup MFA'}
                </Button>
              </div>
            </Form>
          );
        }}
      </Formik>

      {/* {user?.mfaType && (
        <div className='mt-6 p-4 bg-green-50 border border-green-200 rounded-lg'>
          <p className='text-green-800'>
            <strong>Current MFA Method:</strong>{' '}
            {user.mfaType === 'email' ? 'Email' : 'Google Authenticator (TOTP)'}
          </p>
        </div>
      )} */}

      {/* QR Code Modal */}
      <Modal
        visible={showQRModal}
        title={showVerificationStep ? 'Verify Google Authenticator' : 'Setup Google Authenticator'}
        handleVisibility={setShowQRModal}
        closeOnOutsideClick={false}
        classname='max-w-md'
      >
        <div className='text-center space-y-4'>
          {!showVerificationStep ? (
            // QR Code Display Step
            <>
              <p className='text-gray-600 mb-4'>
                Scan this QR code with your Google Authenticator app, then click "Next" to verify.
              </p>

              {qrCodeData?.qr_code && (
                <div className='flex justify-center mb-4'>
                  <img
                    src={qrCodeData.qr_code}
                    alt='QR Code for Google Authenticator'
                    className='border border-gray-200 rounded-lg'
                  />
                </div>
              )}

              {qrCodeData?.secret && (
                <div className='bg-gray-50 p-3 rounded-lg'>
                  <p className='text-sm text-gray-600 mb-2'>
                    <strong>Manual Entry Key:</strong>
                  </p>
                  <p className='text-sm font-mono bg-white p-2 rounded border break-all'>
                    {qrCodeData.secret}
                  </p>
                  <p className='text-xs text-gray-500 mt-2'>
                    Use this key if you can't scan the QR code
                  </p>
                </div>
              )}

              <div className='flex justify-center space-x-3 mt-6'>
                <Button
                  type='button'
                  theme='secondary'
                  onClick={() => {
                    setShowQRModal(false);
                    setQrCodeData(null);
                  }}
                  className='px-6 py-2'
                >
                  Cancel
                </Button>
                <Button
                  type='button'
                  theme='primary'
                  onClick={() => setShowVerificationStep(true)}
                  className='px-6 py-2'
                >
                  Next
                </Button>
              </div>
            </>
          ) : (
            // Verification Step
            <>
              <p className='text-gray-600 mb-4'>
                Enter the 6-digit code from your Google Authenticator app to complete the setup.
              </p>

              <Formik
                initialValues={{ verification_code: '' }}
                validationSchema={verificationSchema}
                onSubmit={handleVerification}
              >
                {({ isSubmitting }) => (
                  <Form className='space-y-4'>
                    <FormikInput
                      name='verification_code'
                      placeholder='Enter 6-digit code'
                      className='text-center text-lg tracking-widest'
                      maxLength={6}
                    />

                    <div className='flex justify-center space-x-3 mt-6'>
                      <Button
                        type='button'
                        theme='secondary'
                        onClick={() => setShowVerificationStep(false)}
                        className='px-6 py-2'
                      >
                        Back
                      </Button>
                      <Button
                        type='submit'
                        theme='primary'
                        isSubmitting={isSubmitting || verificationLoading}
                        disabled={isSubmitting || verificationLoading}
                        className='px-6 py-2'
                      >
                        {isSubmitting || verificationLoading ? 'Verifying...' : 'Verify'}
                      </Button>
                    </div>
                  </Form>
                )}
              </Formik>
            </>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default memo(MFASetup);