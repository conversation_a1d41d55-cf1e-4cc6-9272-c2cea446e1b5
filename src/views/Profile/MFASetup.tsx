import { FC, memo, useMemo } from 'react';
import { Form, Formik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import * as yup from 'yup';
import Button from 'shared-resources/components/Button/Button';
import FormikRadio from 'shared-resources/components/Radio/FormikRadio';
import { RadioItem } from 'shared-resources/types/Radio.type';
import { getUserData, getUserLoading } from 'store/selectors/user.selector';
import { setupMFAAction } from 'store/actions/user.action';
import { UserType } from 'types/enum';

interface MFASetupFormValues {
  mfa_preference: 'email' | 'totp';
}

const mfaSetupSchema = yup.object().shape({
  mfa_preference: yup
    .string()
    .oneOf(['email', 'totp'], 'Please select a valid MFA option')
    .required('Please select an MFA option'),
});

const MFASetup: FC = () => {
  const dispatch = useDispatch();
  const user = useSelector(getUserData);
  const isLoading = useSelector(getUserLoading);

  const mfaOptions: RadioItem[] = useMemo(
    () => [
      {
        label: 'Email',
        value: 'email',
      },
      {
        label: 'Google Authenticator (TOTP)',
        value: 'totp',
      },
    ],
    []
  );

  const initialValues: MFASetupFormValues = {
    mfa_preference: user?.mfaType || 'email',
  };

  const handleSubmit = (values: MFASetupFormValues) => {
    if (!user?.email || !user?.type) {
      return;
    }

    dispatch(
      setupMFAAction({
        email: user.email,
        mfa_preference: values.mfa_preference,
        user_type: user.type as UserType,
        onSuccess: (message: string) => {
          console.log('MFA setup successful:', message);
        },
        onError: (error: string) => {
          console.error('MFA setup failed:', error);
        },
      })
    );
  };

  return (
    <div className='bg-white p-6 rounded-lg shadow-sm'>
      <div className='mb-6'>
        <h2 className='text-xl font-semibold text-gray-900 mb-2'>
          Multi-Factor Authentication Setup
        </h2>
        <p className='text-gray-600'>
          Choose your preferred method for multi-factor authentication to enhance your account security.
        </p>
      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={mfaSetupSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ isSubmitting }) => (
          <Form className='space-y-6'>
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-3'>
                Select MFA Method
              </label>
              <FormikRadio
                name='mfa_preference'
                options={mfaOptions}
                className='space-y-3'
                className2='p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors'
                inputClassName='text-blue-600 focus:ring-blue-500'
                labelClassName='text-gray-900 font-medium'
              />
            </div>

            <div className='pt-4'>
              <Button
                type='submit'
                isSubmitting={isSubmitting || isLoading}
                disabled={isSubmitting || isLoading}
                className='px-6 py-2'
              >
                {isSubmitting || isLoading ? 'Setting up...' : 'Setup MFA'}
              </Button>
            </div>
          </Form>
        )}
      </Formik>

      {user?.mfaType && (
        <div className='mt-6 p-4 bg-green-50 border border-green-200 rounded-lg'>
          <p className='text-green-800'>
            <strong>Current MFA Method:</strong>{' '}
            {user.mfaType === 'email' ? 'Email' : 'Google Authenticator (TOTP)'}
          </p>
        </div>
      )}
    </div>
  );
};

export default memo(MFASetup);