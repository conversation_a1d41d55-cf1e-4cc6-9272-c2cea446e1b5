import React, { useState, useEffect } from 'react';
import { Form, Formik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import * as yup from 'yup';
import { addSeconds, differenceInSeconds } from 'date-fns';
import { authLoginAction, resendMfaCode } from 'store/actions/auth.action';
import { UserType } from 'types/enum';
import { localStorageService } from 'services/LocalStorageService';
import logo from '../assets/ExitSmartsLogo.svg';
import { isAuthLoadingSelector } from '../store/selectors/auth.selector';
import { getMFADetails } from 'store/selectors/user.selector';
import {
  verifyMFATOTPAction,
} from 'store/actions/user.action';

interface Props {
  email?: string;
}
interface MfaDetails {
  mfa_preference: 'email' | 'totp';
  email: string;
  user_type: UserType;
  secret: string;
}

const MfaVerification: React.FC<Props> = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const location = useLocation();
  const state = location.state as Props | null;
  const email = !searchParams.get('email')
    ? state?.email
    : searchParams.get('email');
  const { pathname } = location;
  const currentPath = pathname.split('/')[1];
  const isAuthLoading = useSelector(isAuthLoadingSelector);

  const mfaDetails = useSelector(getMFADetails) as MfaDetails;

  const verificationSchema =
    mfaDetails.mfa_preference === 'email'
      ? yup.object().shape({
          verificationCode: yup
            .string()
            .required('Verification code is required')
            .matches(/^\d{8}$/, 'Must be exactly 8 digits'),
        })
      : yup.object().shape({
          authenticatorCode: yup
            .string()
            .required('Authenticator code is required')
            .matches(/^\d{6}$/, 'Must be exactly 6 digits'),
        });

  const initialValues =
    mfaDetails.mfa_preference === 'email'
      ? { verificationCode: '' }
      : { authenticatorCode: '' };

  const [resendAvailableAt, setResendAvailableAt] = useState<Date | null>();
  const [remainingSeconds, setRemainingSeconds] = useState<number>(60);

  const handleSubmit = (values: {
    verificationCode?: string;
    authenticatorCode?: string;
  }) => {
    if (mfaDetails.mfa_preference === 'email') {
      dispatch(
        authLoginAction({
          code: values.verificationCode ?? '',
          userType: currentPath as UserType,
        })
      );
    } else {
      dispatch(
        verifyMFATOTPAction({
          email: email ?? '',
          verification_code: values.authenticatorCode ?? '',
          user_type: mfaDetails.user_type,
          secret: mfaDetails.secret,
          onSuccess: (message: string) => {
            console.error('MFA verification success:', message);
          },
          onError: (error: string) => {
            console.error('MFA verification failed:', error);
          },
        })
      );
    }
  };

  const handleResendCode = () => {
    if (!resendAvailableAt || remainingSeconds <= 0) {
      const nextAvailableAt = addSeconds(new Date(), 61);
      setResendAvailableAt(nextAvailableAt);
    }
    dispatch(
      resendMfaCode({
        userType: currentPath as UserType,
      })
    );
  };

  useEffect(() => {
    if (!resendAvailableAt || remainingSeconds <= 0) {
      const nextAvailableAt = addSeconds(new Date(), 61);
      setResendAvailableAt(nextAvailableAt);
    }
  }, []);
  useEffect(() => {
    if (!localStorageService.getLocalStorageValue('mfa_token')) {
      navigate(`/${currentPath}/login`);
    }
  }, [localStorageService]);

  useEffect(() => {
    if (resendAvailableAt) {
      const interval = setInterval(() => {
        const secondsLeft = differenceInSeconds(resendAvailableAt, new Date());
        if (secondsLeft <= 0) {
          setRemainingSeconds(0);
          setResendAvailableAt(null);
          clearInterval(interval);
        } else {
          setRemainingSeconds(secondsLeft);
        }
      }, 1000);

      return () => clearInterval(interval); // Cleanup function
    }

    // Return nothing if no `resendAvailableAt`
    return undefined;
  }, [resendAvailableAt]);

  return (
    <div className='flex flex-col justify-center space-y-8 items-center h-screen'>
      <img src={logo} alt='' className='h-28 w-135' />
      <div className='w-135 bg-gray-01 rounded-xl p-12'>
        <Formik
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={verificationSchema}
          validateOnBlur
        >
          {(formikProps) => (
            <Form
              onSubmit={formikProps.handleSubmit}
              className='h-full w-full flex flex-col justify-between'
            >
              <div className='space-y-4'>
                <h1 className='text-blue-01 text-xl font-bold text-center'>
                  Two Factor Authentication
                </h1>
                {mfaDetails.mfa_preference === 'email' && (
                  <>
                    <p className='text-black font-medium text-center'>
                      We&rsquo;ve sent a verification code to:
                    </p>
                    <p className='text-blue-01 font-medium text-center'>
                      {email}
                    </p>
                    <p className='text-black-02 text-sm text-center'>
                      Please enter the 8-digit verification code below
                    </p>
                  </>
                )}
                {mfaDetails.mfa_preference === 'totp' && (
                  <>
                    <p className='text-black font-medium text-center'>
                      Enter the 6-digit code from your Google Authenticator app
                    </p>
                  </>
                )}
              </div>

              <div className='my-8'>
                {mfaDetails.mfa_preference === 'email' && (
                  <FormikInput
                    name='verificationCode'
                    id='verificationCode'
                    type='text'
                    placeholder='Enter 8-digit code'
                    className='h-12.5 !border-gray-02 outline-none rounded-xl'
                    labelClassName='mb-2.5'
                    label='Verification Code'
                    requiredLabel
                    maxLength={8}
                    disabled={isAuthLoading}
                  />
                )}
                {mfaDetails.mfa_preference === 'totp' && (
                  <FormikInput
                    name='authenticatorCode'
                    id='authenticatorCode'
                    type='text'
                    placeholder='Enter 6-digit code'
                    className='h-12.5 !border-gray-02 outline-none rounded-xl'
                    labelClassName='mb-2.5'
                    label='Authenticator Code'
                    requiredLabel
                    maxLength={6}
                    disabled={isAuthLoading}
                  />
                )}
              </div>

              <div className='flex flex-col gap-4'>
                <Button
                  className='h-13'
                  type='submit'
                  disabled={
                    !(formikProps.isValid && formikProps.dirty) || isAuthLoading
                  }
                  isSubmitting={isAuthLoading}
                >
                  <div className='flex justify-center font-semibold items-center gap-2'>
                    <span>Verify</span>
                  </div>
                </Button>

                {mfaDetails.mfa_preference === 'email' && (
                  <div className='text-center'>
                    <button
                      type='button'
                      onClick={handleResendCode}
                      className={`font-medium ${
                        remainingSeconds > 0
                          ? 'text-blue-300'
                          : 'text-blue-01 hover:underline '
                      }`}
                      disabled={remainingSeconds > 0}
                    >
                      {remainingSeconds > 0
                        ? `Resend in ${remainingSeconds} sec`
                        : 'Didn’t receive the code? Resend'}
                    </button>
                  </div>
                )}
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default MfaVerification;
