import { User } from 'models/entities/User';
import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import ConfirmYourAccountModal from 'shared-resources/components/BusinessOwner/Modals/ConfirmYourAccountModal';
import SetPasswordModal from 'shared-resources/components/BusinessOwner/Modals/SetPasswordModal';
import Button from 'shared-resources/components/Button/Button';
import Modal from 'shared-resources/components/Modal/Modal';
import { setPublicPasswordAction } from 'store/actions/forgot-password.action';
import { RouteKey, UserRouteType } from 'types/enum';
import thankYou from '../../assets/thankyou.svg';

interface Props {
  loggedInUserData?: User;
  token?: string | null;
  location?: {
    pathname: string;
  };
  isPasswordSet?: boolean;
  pageContent?: string;
  businessOwnerProperties?: {
    [key: string]: any;
  };
}
const ThankYouPage: React.FC<Props> = ({
  loggedInUserData,
  token,
  isPasswordSet,
  location,
  pageContent,
  businessOwnerProperties,
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [confirmYourAccountModalVisible, setConfirmYourAccountModalVisibility] =
    useState<boolean>(false);
  const [setPasswordModalVisible, setSetPasswordModalVisibility] =
    useState<boolean>(false);

  const handleConfirmYourAccountModalVisibility = (value: boolean) => {
    setConfirmYourAccountModalVisibility(value);
  };
  const handleSetPasswordModalVisibility = (value: boolean) => {
    setSetPasswordModalVisibility(value);
  };
  const handleOnConfirmAccount = () => {
    setSetPasswordModalVisibility(true);
  };

  const onSubmitConfirmAccount = (values: boolean) => {
    if (values) {
      setSetPasswordModalVisibility(true);
      setConfirmYourAccountModalVisibility(false);
    }
  };
  const onSubmitSetPassword = (values: any) => {
    dispatch(
      setPublicPasswordAction({
        token: token as string,
        newPassword: values.newPassword,
        onSuccess: () => {
          setSetPasswordModalVisibility(false);
          navigate(`/${UserRouteType.BUSINESS_OWNER}/${RouteKey.LOGIN}`);
        },
      })
    );
  };
  return (
    <div className='rounded-xl border bg-white h-[calc(100vh-9.6875rem)] w-full flex flex-col justify-center items-center space-y-6'>
      <img src={thankYou} alt='Thank You' />
      <h1 className='text-lg font-semibold'>Thank You</h1>
      <h1 className='max-w-[40rem] px-2 text-center font-medium'>
        {pageContent ||
          `You have completed Readiness Assessment! Thank you for taking time out
        of your busy day. Your advisor will be in touch to discuss results.`}
      </h1>
      {location?.pathname?.includes('public') && !isPasswordSet && (
        <Button onClick={() => handleOnConfirmAccount()} className='px-6 py-2'>
          Confirm Your Account
        </Button>
      )}
      {location?.pathname?.includes('public') && (
        <Button
          onClick={() => navigate('/business-owner/login/')}
          className='px-6 py-2'
        >
          Back To Login
        </Button>
      )}
      <div className='space-x-10 flex items-center'>
        {!location?.pathname?.includes('public') && (
          <Button
            onClick={() =>
              navigate(`/${loggedInUserData?.type}/${RouteKey.DASHBOARD}`)
            }
            className='px-6 py-2'
          >
            Back To Dashboard
          </Button>
        )}
        {/* {loggedInUserData?.type === UserType.ADVISOR && (
          <Link
            className='text-blue-01 font-medium cursor-pointer'
            to={
              assessementType === 'Readiness'
                ? `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${id}/readiness-report`
                : `/${UserRouteType.ADVISOR}/${UserRouteType.BUSINESS_OWNER}/${id}/transition-report`
            }
          >
            {viewReport && (
              <div className='flex items-center'>
                View Report <FaArrowUp className='rotate-45 ml-2' />
              </div>
            )}
          </Link>
        )} */}
      </div>
      <Modal
        visible={confirmYourAccountModalVisible}
        handleVisibility={handleConfirmYourAccountModalVisibility}
        closeOnOutsideClick
      >
        <ConfirmYourAccountModal
          handleConfirmYourAccountModalVisibility={
            handleConfirmYourAccountModalVisibility
          }
          onSubmitConfirmAccount={onSubmitConfirmAccount}
        />
      </Modal>
      <Modal
        visible={setPasswordModalVisible}
        title='Set Your Password'
        handleVisibility={handleSetPasswordModalVisibility}
        closeOnOutsideClick
      >
        <SetPasswordModal
          handleSetPasswordModalVisibility={handleSetPasswordModalVisibility}
          onSubmitSetPassword={onSubmitSetPassword}
          businessOwnerProperties={businessOwnerProperties}
        />
      </Modal>
    </div>
  );
};

export default ThankYouPage;
