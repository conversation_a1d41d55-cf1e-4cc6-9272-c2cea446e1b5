import { Form, Formik } from 'formik';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import Button from 'shared-resources/components/Button/Button';
import FormikInput from 'shared-resources/components/Input/FormikInput';
import { resetPasswordAction } from 'store/actions/forgot-password.action';
import { resetPasswordLoadingSelector } from 'store/selectors/forgot-password.selectors';
import { UserRouteType, UserType } from 'types/enum';
import * as yup from 'yup';
import { passwordSchema } from 'utils/helpers/Helpers';
import logo from '../assets/ExitSmartsLogo.svg';

const forgotPasswordInSchema = yup.object().shape({
  password: passwordSchema,
  confirmPassword: yup
    .string()
    .required('Confirm Password is required')
    .oneOf([yup.ref('password')], 'Confirm Password must match'),
});

interface ResetPasswordForm {
  password: string;
  confirmPassword: string;
}
const ResetPassword: React.FC = () => {
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const path = location.pathname.split('/')[1] ?? UserType.ADVISOR;
  const resetPasswordLoading = useSelector(resetPasswordLoadingSelector);

  const initialValues: ResetPasswordForm = {
    password: '',
    confirmPassword: '',
  };
  const navigate = useNavigate();
  const token = searchParams.get('token');

  const handleSubmit = (values: ResetPasswordForm) => {
    if (token) {
      dispatch(
        resetPasswordAction({
          newPassword: values.password,
          token,
          userType: path as UserRouteType,
          onSuccess: () => {
            navigate(`/${path}/login`);
          },
        })
      );
    } else {
      toast.error('Token not found');
    }
  };

  return (
    <div className='flex flex-col justify-center items-center gap-11 h-screen'>
      <img src={logo} alt='' className='h-28 w-135 ' />
      <div className='w-135 bg-gray-01 rounded-xl p-12'>
        <Formik
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={forgotPasswordInSchema}
        >
          {(formikProps) => (
            <Form
              onSubmit={formikProps.handleSubmit}
              className='h-full w-full flex flex-col justify-between'
            >
              <div className='mb-8.5'>
                <h1 className='text-xl font-medium text-center'>
                  Reset Password
                </h1>
              </div>
              <div className='flex flex-col gap-8.5'>
                <FormikInput
                  name='password'
                  id='password'
                  type='password'
                  placeholder='Enter your password'
                  className='h-12.5 outline-none rounded-xl'
                  label='New Password'
                  error={
                    formikProps.touched.password && formikProps.errors.password
                  }
                />
                <FormikInput
                  name='confirmPassword'
                  id='confirmPassword'
                  type='password'
                  placeholder='Enter your password'
                  className='h-12.5 outline-none rounded-xl'
                  label='Confirm New Password'
                  error={
                    formikProps.touched.confirmPassword &&
                    formikProps.errors.confirmPassword
                  }
                />
              </div>
              <Button
                className='h-13 mt-8.5'
                type='submit'
                disabled={
                  !formikProps.isValid &&
                  !formikProps.dirty &&
                  !resetPasswordLoading
                }
                isSubmitting={resetPasswordLoading}
              >
                <div className='flex justify-center font-medium items-center gap-2'>
                  Submit
                </div>
              </Button>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default ResetPassword;
