/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */
import React, { useEffect, useState } from 'react';

import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import BackNextComponent from 'shared-resources/components/BackNextComponent';
import Spinner from 'shared-resources/components/Spinner/Spinner';
import {
  fetchBusinessOwnerAssessment,
  fetchOwnAssessment,
  updateBusinessOwnerAssessment,
  updateOwnAssessment,
} from 'store/actions/assessment-tool.action';
import { resetAssessmentData } from 'store/reducers/assessment-tool.reducer';
import {
  getAssessmentToolLoading,
  getAssessmentToolReEvaluate,
  getAssessmentToolResponse,
  getAssessmentToolStatus,
  getBusinessOwnerProperties,
} from 'store/selectors/assessment-tool.selector';
import { getUserData } from 'store/selectors/user.selector';
import {
  AssessmentResponseType,
  AssessmentToolProgressStatus,
  AssessmentTools,
  OwnerRelianceTabs,
  RouteKey,
  UserType,
} from 'types/enum';
import ThankYouPage from 'views/layout/ThankYouPage';
import WithComments from 'shared-resources/components/ToolComments/WithComments';
import {
  enumTextToOptionsText,
  getUserRouteType,
} from '../../utils/helpers/Helpers';
import GetStartedPage from './GetStartedPage';
import ManagementTeam from './ManagementTeam';
import {
  handleBackTabSwitch,
  handleNextTabSwitch,
  headerOptions,
  isOwnerInTeam,
  numberToTabObject,
  ownerRelianceResponse,
  tabToNumberObject,
} from './OwnerRelianceConfig';
import TableComponent from './TableComponent';

const OwnerReliance: React.FC = () => {
  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const loggedInUserData = useSelector(getUserData);
  const assessmentToolStatus = useSelector(getAssessmentToolStatus);
  const dispatch = useDispatch();
  const savedResponse: any = useSelector(getAssessmentToolResponse);
  const [onClickError, setOnclickError] = useState<boolean>(false);
  const isLoading = useSelector(getAssessmentToolLoading);
  const [submitType, setSubmitType] = useState<AssessmentResponseType | null>(
    null
  );
  const [response, setResponse] = useState(ownerRelianceResponse);
  const [currentTab, setCurrentTab] = useState<
    | {
        tab: OwnerRelianceTabs;
      }
    | undefined
  >();
  const businessOwnerProperties: any = useSelector(getBusinessOwnerProperties);
  const assessmentReEvaluate = useSelector(getAssessmentToolReEvaluate);
  const navigate = useNavigate();

  useEffect(() => {
    if (
      assessmentToolStatus === AssessmentToolProgressStatus.COMPLETED &&
      assessmentReEvaluate &&
      !isLoading
    ) {
      dispatch(resetAssessmentData());
    }
  }, [assessmentReEvaluate, loggedInUserData, assessmentToolStatus]);

  useEffect(() => {
    const tab = new URLSearchParams(currentTab as Record<string, string>);
    setSearchParams(tab);
  }, [currentTab, setSearchParams, searchParams]);

  // set saved tab if assessment is not completed
  useEffect(() => {
    if (
      assessmentToolStatus !== AssessmentToolProgressStatus.COMPLETED &&
      savedResponse?.saved_tab
    ) {
      setCurrentTab({ tab: numberToTabObject[savedResponse?.saved_tab] });
    } else {
      setCurrentTab({ tab: OwnerRelianceTabs.GET_STARTED });
    }
  }, [savedResponse]);

  const handleFetchAssessmentError = () => {
    navigate(
      `/${getUserRouteType(loggedInUserData?.type as UserType)}/${
        RouteKey.DASHBOARD
      }`
    );
  };
  // fetch saved assesment
  useEffect(() => {
    if (loggedInUserData?.type === UserType.ADVISOR && id) {
      dispatch(
        fetchBusinessOwnerAssessment({
          tool: AssessmentTools.OWNER_RELIANCE,
          businessOwnerId: +id!,
          onError: handleFetchAssessmentError,
        })
      );
    } else if (loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      dispatch(
        fetchOwnAssessment({
          tool: AssessmentTools.OWNER_RELIANCE,
          onError: handleFetchAssessmentError,
        })
      );
    }
  }, []);

  // set saved response
  useEffect(() => {
    if (
      savedResponse &&
      assessmentToolStatus !== AssessmentToolProgressStatus.COMPLETED
    ) {
      const members: any[] = savedResponse?.management_team?.members;
      const ownerName = `${businessOwnerProperties?.first_name} ${businessOwnerProperties?.last_name}`;
      if (members && ownerName && !isOwnerInTeam(members, ownerName)) {
        setResponse({
          ...savedResponse,
          [OwnerRelianceTabs.MANAGEMENT_TEAM]: {
            ...savedResponse[OwnerRelianceTabs.MANAGEMENT_TEAM],
            members: [...members, { name: ownerName }],
          },
        });
      } else {
        setResponse(savedResponse);
      }
    } else {
      setResponse(ownerRelianceResponse);
    }
  }, [savedResponse, assessmentToolStatus]);

  // update response
  const handleFormSubmit = (saveAsDraft: boolean, savedTab?: number) => {
    let updatedResponse = response;
    if (savedTab) {
      updatedResponse = {
        ...response,
        saved_tab: savedTab,
      };
    }
    if (!saveAsDraft && loggedInUserData?.type === UserType.BUSINESS_OWNER) {
      updatedResponse = {
        ...updatedResponse,
        saved_tab: tabToNumberObject[OwnerRelianceTabs.OTHERS],
      };
    }
    if (loggedInUserData?.type === UserType.ADVISOR && id) {
      dispatch(
        updateBusinessOwnerAssessment({
          businessOwnerId: +id!,
          tool: AssessmentTools.OWNER_RELIANCE,
          assessment_response: updatedResponse,
          submit_type: saveAsDraft
            ? AssessmentResponseType.DRAFT
            : AssessmentResponseType.COMPLETE,
        })
      );
    } else {
      dispatch(
        updateOwnAssessment({
          tool: AssessmentTools.OWNER_RELIANCE,
          assessment_response: updatedResponse,
          submit_type: AssessmentResponseType.DRAFT,
          onSuccess: () =>
            !saveAsDraft &&
            navigate(`${UserType.BUSINESS_OWNER}/${RouteKey.DASHBOARD}`),
        })
      );
    }
  };

  useEffect(() => {
    if (
      Object.keys(businessOwnerProperties).length &&
      assessmentToolStatus !== AssessmentToolProgressStatus.IN_PROGRESS
    ) {
      const members =
        businessOwnerProperties?.management_team_name ||
        JSON.parse(businessOwnerProperties?.management_team || '[]');
      const ownerName = `${businessOwnerProperties?.first_name} ${businessOwnerProperties?.last_name}`;

      setResponse({
        ...ownerRelianceResponse,
        [OwnerRelianceTabs.MANAGEMENT_TEAM]: {
          ...response[OwnerRelianceTabs.MANAGEMENT_TEAM],
          first_name: businessOwnerProperties?.first_name,
          last_name: businessOwnerProperties?.last_name,
          business_name: businessOwnerProperties?.business_name,
          business_start_date: businessOwnerProperties?.business_start_date,
          members: !isOwnerInTeam(members, ownerName)
            ? [...members, { name: ownerName }]
            : members,
        },
      });
    }
  }, [businessOwnerProperties, assessmentToolStatus]);

  const handleBackButton = () => {
    handleBackTabSwitch(currentTab, setCurrentTab);
  };

  function findEmptyKeys(
    object:
      | {
          [key: string]: {
            selected_option: string;
            current_function_owner: string;
          };
        }
      | {
          selected_option: string;
          current_function_owner: string;
          business_function_name: string;
        }[]
  ) {
    const emptyKeys: string[] = [];
    if (Array.isArray(object)) {
      object.forEach((key) => {
        if (
          key.selected_option !== headerOptions[1] &&
          (key.selected_option === '' ||
            key.current_function_owner === '' ||
            key.selected_option === null ||
            key.current_function_owner === null)
        ) {
          emptyKeys.push(key.business_function_name);
        }
      });
    } else {
      for (const key in object) {
        const entry = object[key];
        if (
          entry.selected_option !== headerOptions[1] &&
          (entry.selected_option === '' ||
            entry.current_function_owner === '' ||
            entry.selected_option === null ||
            entry.current_function_owner === null)
        ) {
          emptyKeys.push(key);
        }
      }
    }
    return emptyKeys;
  }

  const handleNextButton = () => {
    setOnclickError(true);
    const errors = findEmptyKeys(response[currentTab?.tab as string]);

    if (errors?.length) {
      let error = '';
      errors.forEach((key) => {
        error += `${enumTextToOptionsText(key).slice(0, -1)}, `;
      });
      error = error.slice(0, -2);
      toast.error(`${error} fields are required.`);
    } else if (currentTab?.tab === OwnerRelianceTabs.OTHERS) {
      handleFormSubmit(false);
      setSubmitType(AssessmentResponseType.COMPLETE);
    } else {
      handleNextTabSwitch(currentTab, setCurrentTab);
    }
    if (!errors.length) {
      setOnclickError(false);
    }
  };

  const handleSaveAsDraft = () => {
    const savedTab = response?.saved_tab || 0;
    if (currentTab?.tab && savedTab < tabToNumberObject[currentTab.tab]) {
      handleFormSubmit(true, tabToNumberObject[currentTab.tab]);
    } else {
      handleFormSubmit(true);
    }
    setSubmitType(AssessmentResponseType.DRAFT);
  };

  if (isLoading && !submitType) {
    return <Spinner />;
  }

  if (assessmentToolStatus === AssessmentToolProgressStatus.COMPLETED) {
    return (
      <ThankYouPage
        pageContent={`You've completed Owner Reliance! Thank you for taking time out of your busy day. Your advisor will be in touch to discuss results.`}
        loggedInUserData={loggedInUserData}
        isPasswordSet={false}
      />
    );
  }

  return (
    <>
      <h1 className='font-bold text-2xl mb-3'>Owner Reliance</h1>
      <WithComments tool={AssessmentTools.OWNER_RELIANCE}>
        <div className='border flex flex-col justify-between bg-white h-[calc(100vh-13rem)] px-4 py-5 w-full'>
          {currentTab?.tab === OwnerRelianceTabs.GET_STARTED && (
            <GetStartedPage
              onGestartedClick={() =>
                setCurrentTab({ tab: OwnerRelianceTabs.MANAGEMENT_TEAM })
              }
            />
          )}
          {currentTab &&
            currentTab?.tab === OwnerRelianceTabs.MANAGEMENT_TEAM && (
              <ManagementTeam
                response={response}
                setResponse={setResponse}
                onGestartedClick={() =>
                  setCurrentTab({ tab: OwnerRelianceTabs.PRESIDENT_CEO })
                }
              />
            )}
          {currentTab &&
            currentTab?.tab !== OwnerRelianceTabs.GET_STARTED &&
            currentTab?.tab !== OwnerRelianceTabs.MANAGEMENT_TEAM && (
              <TableComponent
                currentTab={currentTab?.tab}
                response={response}
                setResponse={setResponse}
                emptyKeys={
                  onClickError
                    ? findEmptyKeys(response[currentTab?.tab as string])
                    : []
                }
              />
            )}
          {currentTab?.tab !== OwnerRelianceTabs.GET_STARTED &&
            currentTab?.tab !== OwnerRelianceTabs.MANAGEMENT_TEAM && (
              <div className='pr-4'>
                <BackNextComponent
                  backStep={handleBackButton}
                  nextStep={handleNextButton}
                  buttonText={
                    currentTab?.tab === OwnerRelianceTabs.OTHERS
                      ? 'Submit'
                      : 'Next'
                  }
                  isLoading={isLoading}
                  isNextDisable={false}
                  onSaveToDraftClick={handleSaveAsDraft}
                />
              </div>
            )}
        </div>
      </WithComments>
    </>
  );
};

export default OwnerReliance;
