import { UserProfileUpdateDTO } from 'dtos/business-owner.dto';
import { baseApiService } from './BaseApiService';

class UserService {
  static getInstance(): UserService {
    return new UserService();
  }

  public async updateBusinessOwnerUserProfile(data: UserProfileUpdateDTO) {
    return baseApiService.post('/business-owner/me/update', data);
  }

  public async updateAdvisorUserProfile(data: UserProfileUpdateDTO) {
    return baseApiService.post('/advisor/me/update', data);
  }

  public async updateAdvisorUserProfileByAdmin(data: UserProfileUpdateDTO, user_id: string) {
    return baseApiService.post('/admin/advisor/update', data, {params: { user_id } });
  }
}

export const userService = UserService.getInstance();
