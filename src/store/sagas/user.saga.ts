import { UserProfileUpdateDTO } from 'dtos/business-owner.dto';
import { all, call, put, takeLatest } from 'redux-saga/effects';
import { userService } from 'services/api-services/UserService';
import { UserActionType } from 'store/actions/actions.constants';

import { UpdateUserProfileActionPayloadType } from 'store/actions/user.action';
import {
  UpdateUserProfileAction,
  UpdateUserProfileLoading,
  userError,
} from 'store/reducers/user.reducer';
import { SagaPayloadType } from 'types/SagaPayload.type';
import { UserType } from 'types/enum';

interface UpdateUserProfileSagaPayloadType extends SagaPayloadType {
  payload: UpdateUserProfileActionPayloadType;
}
function* updateUserProfileSaga(action: UpdateUserProfileSagaPayloadType): any {
  const { userType, onSuccess } = action.payload;
  try {
    let response: any;
    yield put(UpdateUserProfileLoading({ loading: true }));
    if (userType === UserType.ADVISOR) {
      const { userType, user_id, update_by, ...rest } = action.payload;
      const data: UserProfileUpdateDTO = { ...rest };
      if (update_by && update_by === UserType.ADMIN) {
        // If the user is being updated by an admin, call the admin endpoint
        response = yield call(userService.updateAdvisorUserProfileByAdmin, data, String(user_id));
      } else {
        response = yield call(userService.updateAdvisorUserProfile, data);
      }
      
    } else {
      const { userType, ...rest } = action.payload;
      const data: UserProfileUpdateDTO = { ...rest };
      response = yield call(userService.updateBusinessOwnerUserProfile, data);
    }
    yield put(UpdateUserProfileAction(response.user));
    yield put(UpdateUserProfileLoading({ loading: false }));
    onSuccess('Profile updated succesfully!');
  } catch (e: any) {
    yield put(userError({ message: e.message, errors: e.errors }));
    yield put(UpdateUserProfileLoading({ loading: false }));
  }
}

export function* userSaga() {
  yield all([
    takeLatest(UserActionType.UPDATE_USER_PROFILE, updateUserProfileSaga),
  ]);
}
