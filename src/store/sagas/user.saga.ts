import { UserProfileUpdateDTO } from 'dtos/business-owner.dto';
import { all, call, put, select, takeLatest } from 'redux-saga/effects';
import { userService } from 'services/api-services/UserService';
import { UserActionType } from 'store/actions/actions.constants';
import { toast } from 'react-toastify';

import { UpdateUserProfileActionPayloadType, MFASetupActionPayloadType } from 'store/actions/user.action';
import {
  UpdateUserProfileAction,
  UpdateUserProfileLoading,
  userError,
  MFASetupLoading,
} from 'store/reducers/user.reducer';
import { SagaPayloadType } from 'types/SagaPayload.type';
import { UserType } from 'types/enum';

interface UpdateUserProfileSagaPayloadType extends SagaPayloadType {
  payload: UpdateUserProfileActionPayloadType;
}

interface MFASetupSagaPayloadType extends SagaPayloadType {
  payload: MFASetupActionPayloadType;
}
function* updateUserProfileSaga(action: UpdateUserProfileSagaPayloadType): any {
  const { userType, onSuccess } = action.payload;
  try {
    let response: any;
    yield put(UpdateUserProfileLoading({ loading: true }));
    if (userType === UserType.ADVISOR) {
      const { userType, user_id, update_by, ...rest } = action.payload;
      const data: UserProfileUpdateDTO = { ...rest };
      if (update_by && update_by === UserType.ADMIN) {
        // If the user is being updated by an admin, call the admin endpoint
        response = yield call(userService.updateAdvisorUserProfileByAdmin, data, String(user_id));
      } else {
        response = yield call(userService.updateAdvisorUserProfile, data);
      }
      
    } else {
      const { userType, ...rest } = action.payload;
      const data: UserProfileUpdateDTO = { ...rest };
      response = yield call(userService.updateBusinessOwnerUserProfile, data);
    }
    yield put(UpdateUserProfileAction(response.user));
    yield put(UpdateUserProfileLoading({ loading: false }));
    onSuccess('Profile updated succesfully!');
  } catch (e: any) {
    yield put(userError({ message: e.message, errors: e.errors }));
    yield put(UpdateUserProfileLoading({ loading: false }));
  }
}

function* setupMFASaga(action: MFASetupSagaPayloadType): any {
  const { onSuccess, onError } = action.payload;
  try {
    yield put(MFASetupLoading({ loading: true }));
    const response: any = yield call(userService.setupMFA, {
      email: action.payload.email,
      mfa_preference: action.payload.mfa_preference,
      user_type: action.payload.user_type,
    });

    // Update user entity with new MFA type
    if (response.user) {
      yield put(UpdateUserProfileAction(response.user));
    } else {
      // If API doesn't return user data, manually update the MFA type
      const currentUser = yield select((state: any) => {
        const userId = state.user.ids[0];
        return state.user.entities[userId];
      });

      if (currentUser) {
        yield put(UpdateUserProfileAction({
          ...currentUser,
          mfaType: action.payload.mfa_preference,
        }));
      }
    }

    yield put(MFASetupLoading({ loading: false }));
    toast.success('MFA setup completed successfully!');
    onSuccess('MFA setup completed successfully!', response);
  } catch (e: any) {
    yield put(userError({ message: e.message, errors: e.errors }));
    yield put(MFASetupLoading({ loading: false }));
    toast.error(e.message || 'Failed to setup MFA');
    onError?.(e.message || 'Failed to setup MFA');
  }
}

export function* userSaga() {
  yield all([
    takeLatest(UserActionType.UPDATE_USER_PROFILE, updateUserProfileSaga),
    takeLatest(UserActionType.SETUP_MFA, setupMFASaga),
  ]);
}
