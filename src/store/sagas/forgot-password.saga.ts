import { call, put, takeLatest } from 'redux-saga/effects';
import { authService } from 'services/api-services/AuthService';
import { ForgotPasswordActionType } from 'store/actions/actions.constants';
import {
  forgotPasswordError,
  forgotPasswordSuccess,
  resetPasswordError,
  resetPasswordSuccess,
  setForgotPasswordLoading,
  setPublicPasswordError,
  setPublicPasswordLoading,
  setPublicPasswordSuccess,
  setResetPasswordLoading,
} from 'store/reducers/forgot-password.reducer';

import { SagaPayloadType } from 'types/SagaPayload.type';
import { toast } from 'react-toastify';

import {
  ForgotPasswordActionPayloadType,
  ResetPasswordActionPayloadType,
  SetPublicPasswordActionPayloadType,
} from '../actions/forgot-password.action';

interface ForgotPasswordSagaPayloadType extends SagaPayloadType {
  payload: ForgotPasswordActionPayloadType;
}

interface ResetPasswordSagaPayloadType extends SagaPayloadType {
  payload: ResetPasswordActionPayloadType;
}

interface SetPublicPasswordSagaPayloadType extends SagaPayloadType {
  payload: SetPublicPasswordActionPayloadType;
}

function* forgotPasswordSaga(action: ForgotPasswordSagaPayloadType): any {
  const { email, userType } = action.payload;

  try {
    yield put(setForgotPasswordLoading({ loading: true }));
    yield call(authService.forgotPassword, { userType, data: { email } });
    yield put(forgotPasswordSuccess());
    toast.success('Email sent successfully');
  } catch (e: any) {
    yield put(forgotPasswordError(e?.response?.data?.message));
    yield put(setForgotPasswordLoading({ loading: false }));
  }
}

function* resetPasswordSaga(action: ResetPasswordSagaPayloadType): any {
  const { newPassword, token, userType, onSuccess } = action.payload;

  try {
    yield put(setResetPasswordLoading({ loading: true }));
    yield call(authService.resetPassword, {
      userType,
      data: { new_password: newPassword, token },
    });
    yield put(resetPasswordSuccess());
    toast.success('Reset password successful');
    onSuccess();
  } catch (e: any) {
    yield put(resetPasswordError(e?.message));
    yield put(setResetPasswordLoading({ loading: false }));
  }
}
function* setPublicPasswordSaga(action: SetPublicPasswordSagaPayloadType): any {
  const { onSuccess, newPassword, token } = action.payload;
  try {
    yield put(setPublicPasswordLoading({ loading: true }));
    yield call(
      authService.setPublicPassword as (data: {
        new_password: string;
        token: string;
      }) => Promise<{ data: string }>,
      {
        new_password: newPassword,
        token,
      }
    );
    yield put(setPublicPasswordSuccess());
    toast.success('Password set successfully', {
      position: 'top-right',
    });
    if (onSuccess) {
      onSuccess();
    }
  } catch (e: any) {
    yield put(setPublicPasswordError(e?.message));
    yield put(setPublicPasswordLoading({ loading: false }));
  }
}

export function* forgotPasswordSagaWatcher() {
  yield takeLatest(
    ForgotPasswordActionType.FORGOT_PASSWORD,
    forgotPasswordSaga
  );
  yield takeLatest(ForgotPasswordActionType.RESET_PASSWORD, resetPasswordSaga);
  yield takeLatest(
    ForgotPasswordActionType.SET_PUBLIC_PASSWORD,
    setPublicPasswordSaga
  );
}
