import {
  PayloadAction,
  createEntityAdapter,
  createSlice,
} from '@reduxjs/toolkit';
import { BusinessOwner } from 'models/entities/BusinessOwner';
import { User } from '../../models/entities/User';

const userAdapter = createEntityAdapter<User>();

const initialState = {
  ...userAdapter.getInitialState(),
  loading: false,
  error: '',
  errors: [],
  update: { loading: false },
  mfa: { loading: false },
  advisorSubscriptionData: {},
};
export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    userAdd: userAdapter.addOne,
    userUpdate: userAdapter.updateOne,
    userLoading: (state, action: PayloadAction<{ loading: boolean }>) => {
      state.loading = action.payload.loading;
    },
    userError: (
      state,
      action: PayloadAction<{ message: string; errors?: any }>
    ) => {
      state.error = action.payload.message;
      state.errors = action.payload.errors;
    },
    update_user_profile: (state, action: PayloadAction<BusinessOwner>) => {
      userAdapter.updateOne(state, {
        id: action.payload.id,
        changes: action.payload,
      });
      state.update = { ...state.update, loading: !!action.payload };
    },
    update_user_profile_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.update = { ...state.update, loading };
    },
    add_advisor_subscription_data: (
      state,
      action: PayloadAction<{ data: any }>
    ) => {
      state.advisorSubscriptionData = action.payload.data;
    },
    mfa_setup_loading: (
      state,
      action: PayloadAction<{ loading: boolean }>
    ) => {
      const { loading } = action.payload;
      state.mfa = { ...state.mfa, loading };
    },
  },
});

export const {
  userAdd,
  userUpdate,
  userLoading,
  userError,
  update_user_profile: UpdateUserProfileAction,
  update_user_profile_loading: UpdateUserProfileLoading,
  add_advisor_subscription_data: AddAdvisorSubscriptionData,
  mfa_setup_loading: MFASetupLoading,
} = userSlice.actions;

export default userSlice.reducer;
