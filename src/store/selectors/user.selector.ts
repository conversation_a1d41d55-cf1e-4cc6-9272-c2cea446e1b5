import { createSelector } from 'reselect';
import { AppState } from 'store';

const userStore = (store: AppState) => store.user;

const getUserSelector = (state: AppState) => {
  const userId = state.user.ids[0];
  return state.user.entities[userId];
};

export const getUserData = createSelector(getUserSelector, (user) => user);

export const getUserLoading = createSelector(
  [userStore],
  (user) => user.loading
);

export const getUserUpdateProfileLoading = createSelector(
  [userStore],
  (user) => user.update?.loading
);

export const userErrors = createSelector([userStore], (user) => user.errors);

export const getAdvisorSubscriptionData = createSelector(
  [userStore],
  (user) => user.advisorSubscriptionData
);

export const getMFASetupLoading = createSelector(
  [userStore],
  (user) => user.mfa?.loading || false
);

export const getMFADetails = createSelector(
  [userStore],
  (user) => user.mfaDetails
);
