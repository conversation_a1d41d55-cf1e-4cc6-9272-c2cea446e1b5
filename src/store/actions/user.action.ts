import { UserProfileUpdateDTO } from 'dtos/business-owner.dto';
import { UserType } from 'types/enum';
import { UserActionType } from './actions.constants';

export interface UpdateUserProfileActionPayloadType
  extends UserProfileUpdateDTO {
  userType: UserType;
  user_id?: string;
  update_by?: string;
  onSuccess: (message: string) => void;
}
export const updateUserProfileAction = (
  payload: UpdateUserProfileActionPayloadType
) => ({
  type: UserActionType.UPDATE_USER_PROFILE,
  payload,
});

export interface MFASetupActionPayloadType {
  email: string;
  mfa_preference: 'email' | 'totp';
  user_type: UserType;
  onSuccess: (message: string, data?: any) => void;
  onError?: (error: string) => void;
}

export const setupMFAAction = (
  payload: MFASetupActionPayloadType
) => ({
  type: UserActionType.SETUP_MFA,
  payload,
});

export interface VerifyMFASetupActionPayloadType {
  email: string;
  verification_code: string;
  user_type: UserType;
  secret: string;
  onSuccess: (message: string) => void;
  onError?: (error: string) => void;
}

export const verifyMFASetupAction = (
  payload: VerifyMFASetupActionPayloadType
) => ({
  type: UserActionType.VERIFY_MFA_SETUP,
  payload,
});

export const verifyMFATOTPAction = (
  payload: VerifyMFASetupActionPayloadType
) => ({
  type: UserActionType.VERIFY_MFA_TOTP,
  payload,
});
