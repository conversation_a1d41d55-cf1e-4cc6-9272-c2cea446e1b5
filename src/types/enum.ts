export enum UserType {
  BUSINESS_OWNER = 'business_owner',
  ADVISOR = 'advisor',
  ADMIN = 'admin',
}

export enum UserRouteType {
  BUSINESS_OWNER = 'business-owner',
  ADVISOR = 'advisor',
  ADMIN = 'admin',
}

export enum RouteKey {
  DASHBOARD = 'dashboard',
  ADVISOR_INVITE = 'advisor-invite',
  AWARENESS = 'awareness',
  PLAN_DEVELOPMENT = 'plan-development',
  READINESS_ASSESSMENT_DASHBOARD = 'readiness-dashboard',
  TRANSITION_OBJECTIVES_DASHBOARD = 'transition-dashboard',
  READINESS_ASSESSMENT = 'readiness-assessment',
  TRANSITION_OBJECTIVES = 'transition-objectives',
  BUSINESS_CONTINUITY = 'business-continuity',
  ASSET_PROTECTION = 'asset-protection',
  ASSET_PROTECTION_DASHBOARD = 'asset-protection-dashboard',
  ASSET_PROTECTION_REPORT = 'asset-protection-report',
  BUSINESS_CONTINUITY_DASHBOARD = 'business-continuity-dashboard',
  OWNER_RELIANCE_DASHBOARD = 'owner-reliance-dashboard',
  OWNER_RELIANCE = 'owner-reliance',
  BUSINESS_VALUATION_DASHBOARD = 'valuation-dashboard',
  BUSINESS_VALUATION = 'business-valuation',
  VALUE_ENHANCEMENT_OPPORTUNITIES = 'value-enhancement-opportunities',
  VALUE_ENHANCEMENT_OPPORTUNITIES_REPORT = 'value-enhancement-opportunities-report',
  STAKEHOLDER_ALIGNMENT_MEETING = 'stakeholder-alignment-meeting',
  REPORTS = 'reports',
  READINESS_REPORT = 'readiness-report',
  TRANSITION_REPORT = 'transition-report',
  OWNER_RELIANCE_REPORT = 'owner-reliance-report',
  BUSINESS_CONTINUITY_REPORT = 'business-continuity-report',
  VALUE_ENHANCEMENT_OPPORTUNITIES_DASHBOARD = 'value-enhancement-dashboard',
  STAKEHOLDER_ALIGNMENT_MEETING_DASHBOARD = 'stakeholder-alignment-dashboard',
  FINANCIAL_GAP_ANALYSIS_DASHBOARD = 'financial-gap-dashboard',
  BUYER_TYPE_DEAL_STRUCTURE = 'buyer-type-deal-structure',
  BUYER_TYPE_DEAL_STRUCTURE_REPORT = 'buyer-type-deal-structure-report',
  BUYER_TYPE_DEAL_STRUCTURE_DASHBOARD = 'buyer-type-deal-structure-dashboard',
  PAYMENT = 'payment',
  PROFILE = 'profile',
  LEARN_MORE = 'learn-more',
  SUBSCRIPTION = 'subscription',
  BILLING = 'billing',
  FINANCIAL_GAP_ANALYSIS = 'financial-gap-analysis',
  EXPENSE_CALCULATOR = 'expense-calculator',
  FINANCIAL_GAP_REPORT = 'financial-gap-report',
  BUSINESS_FINANCIAL_ANALYSIS = 'business-financial-analysis',
  // logout
  LOGOUT = 'logout',
  LOGIN = 'login',
  REPORT_BUILDER = 'report-builder',

  BO_LIST = 'business-owner-list',
}

export enum AgeRange {
  THIRTY_FORTY = '30 - 40',
  FORTY_FORTYFIVE = '40 - 45',
  FORTYFIVE_FIFTY = '45 - 50',
  FIFTY_FIFTYFIVE = '50 - 55',
  FIFTYFIVE_SIXTY = '55 - 60',
  SIXTY_SIXTYFIVE = '60 - 65',
  SIXTYFIVE_PLUS = '65 +',
}

export enum RevenueRange {
  LESS_FIVE_MILLION = 'less than $5 Million',
  FIVE_TEN_MILLION = '$5-$10 Million',
  TEN_FIFTEEN_MILLION = '$10-$15 Million',
  FIFTEEN_TWENTY_MILLION = '$15-$20 Million',
  TWENTY_FIFTY_MILLION = '$20-$50 Million',
  FIFTY_MILLION_PLUS = '$50 Million Plus',
}

export enum AssessmentTools {
  READINESS_ASSESSMENT = 'readiness_assessment',
  TRANSITION_OBJECTIVES = 'transition_objectives',
  OWNER_RELIANCE = 'owner_reliance',
  BUSINESS_VALUATION = 'business_valuation',
  BUSINESS_CONTINUITY = 'business_continuity',
  VALUE_ENHANCEMENT_OPPORTUNITIES = 'value_enhancement_opportunities',
  FINANCIAL_GAP_ANALYSIS = 'financial_gap_analysis',
  STAKEHOLDER_ALIGNMENT_MEETING = 'stakeholder_alignment_meeting',
  ASSET_PROTECTION = 'asset_protection',
  BUYER_TYPE_DEAL_STRUCTURE = 'buyer_type_deal_structure',
}

export enum NonAssessmentToolTabs {
  BUSINESS_FINANCIAL_ANALYSIS = 'business_financial_analysis',
}

export enum ReportType {
  BUILDER = 'builder',
  OTHER_FA = 'other:fa',
  BUSINESS_VALUATION = 'assessment:business_valuation',
  ASSET_PROTECTION = 'assessment:asset_protection',
}

export enum InviteStatus {
  ACCEPTED = 'accepted',
  CANCELLED = 'cancelled',
  REVOKED = 'revoked',
  PENDING = 'pending',
}

export enum AssessmentStage {
  AWARENESS = 'awareness',
  PLAN_DEVELOPMENT = 'plan_development',
}
export enum AssessmentToolStatus {
  ENABLE = 'enable',
  DISABLE = 'disable',
}

export enum BusinessValuationScreens {
  PAYMENT_SCREEN = 'payment_screen',
  BASIC_INFORMATION = 'basic_information',
  INFO_SCREEN = 'info_screen',
  OWNER_P_AND_L = 'owner_p_and_l',
  SPOUSE_OR_FAMILY_P_AND_L = 'spouse_or_family_p_and_l',
  OWNER_INSURANCE_P_AND_L = 'owner_insurance_p_and_l',
  VEHICLE_P_AND_L = 'vehicle_p_and_l',
  RENT_ADJUSTMENTS_P_AND_L = 'rent_adjustments_p_and_l',
  NON_ACTIVE_FAMILY_BENEFITS_P_AND_L = 'non_active_family_benefits_p_and_l',
  PERSONAL_EXPENSES_P_AND_L = 'personal_expenses_p_and_l',
  TOTAL_NORMALIZATION_EXPENSES = 'total_normalization_expenses',
  INCOME_STATEMENT_DATA = 'income_statement_data',
  BALANCE_SHEET_DATA_1 = 'balance_sheet_data_1',
  BALANCE_SHEET_DATA_2 = 'balance_sheet_data_2',
  BALANCE_SHEET_DATA_3 = 'balance_sheet_data_3',
}

export enum OwnershipTypes {
  PARTNERSHIP = 'partnership',
  LLC = 'llc',
  C_CORP = 'c_corp',
  S_CORP = 's_corp',
  SOLO_PROPRIETERSHIP = 'solo_proprietorship',
  PROFESSIONAL_CORP = 'professional_corp',
}

export enum ValuationReasons {
  SEEKING_FINCANCING = 'seeking_financing',
  LOOKING_TO_SELL_BUSINESS = 'looking_to_sell_business',
  ESTATE_PLANNING = 'estate_planning',
  INSURANCE_AND_RISK_PLANNING = 'insurance_and_risk_planning',
  STRATRIC_PLANNING = 'strategic_planning',
  OTHER = 'other',
}

export enum ImpactType {
  STAY_THE_SAME = 'stay_the_same',
  DECLINE_MINIMALLY = 'decline_minimally',
  DECLINE_MODERATELY = 'decline_moderately',
  FALL_SUBSTANTIALLY = 'fall_substantially',
  PLUMMET = 'plummet',
}

export enum AssessmentResponseType {
  DRAFT = 'draft',
  COMPLETE = 'complete',
}

export enum AssessmentToolProgressStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
}

export enum ProfileSections {
  GENERAL = 'GENERAL',
  COMPANY_DETAILS = 'COMPANY_DETAILS',
  PASSWORD = 'PASSWORD',
  BILLING = 'BILLING',
  MFA = 'MFA',
}

export enum OwnerRelianceTabs {
  GET_STARTED = 'get_started',
  MANAGEMENT_TEAM = 'management_team',
  PRESIDENT_CEO = 'president_ceo',
  SALES_MARKETING = 'sales_marketing',
  FINANCE = 'finance',
  OPERATIONS = 'operations',
  HUMAN_RESOURCES = 'human_resources',
  RESEARCH_AND_DEVELOPMENT = 'research_and_development',
  INFORMATION_TECHNOLOGY = 'information_technology',
  OTHERS = 'others',
}

export enum ValueEnhancementTabs {
  GET_STARTED = 'get_started',
  GROWTH = 'growth',
  OWNER_RELIANCE = 'owner_reliance',
  LEADERSHIP_TEAM = 'leadership_team',
  EMPLOYEE = 'employee',
  PRODUCT = 'product',
  CUSTOMERS = 'customers',
  FINANCIAL = 'financial',
  SYSTEMS_PROCESSES = 'systems_processes',
  SALES = 'sales',
  MARKETING = 'marketing',
  PROPERTY_PLANT_EQUIPMENT = 'property_plant_equipment',
  LEGAL = 'legal',
}

export enum SubscriptionType {
  MONTHLY = 'monthly',
  ANNUALLY = 'yearly',
}

export enum FollowUpType {
  MANUAL = 'manual',
  AUTOMATIC = 'automatic',
}

export enum TransitionObjectivesTabs {
  GET_STARTED = 'get_started',
  OWNERSHIP_DETAILS = 'ownership_details',
  QUESTIONS = 'questions',
}

export enum StakeHolderAlignmentMeetingTabs {
  GET_STARTED = 'get_started',
  STAKEHOLDERS = 'stakeholders',
  STATE_OBJECTIVE = 'state_objective',
}
export enum BusinessContinuityScreens {
  GET_STARTED = 'get_started',
  BASIC_INFORMATION = 'basic_information',
  SOLE_OWNERS = 'sole_owners',
  AUTHORIZED_OWNERS = 'authorized_owners',
  FIRST_PEOPLE_1 = 'first_people_1',
  FIRST_PEOPLE_2 = 'first_people_2',
  INTERIM_OWNERS = 'interim_owners',
  CORPORATE_OFFICERS = 'corporate_officers',
  ADDRESS_EMPLOYEE_PEOPLE = 'address_employee_people',
  ADDRESS_CUSTOMERS_PEOPLE = 'address_customers_people',
  ADDRESS_VENDORS_PEOPLE = 'address_vendors_people',
  BOARD_CHAIRMAN = 'board_chairman',
  INTERIM_CHAIRMAN = 'interim_chairman',
  ACCOUNTING_INFORMATION = 'accounting_information',
  ATTORNEYS_INFORMATION = 'attorneys_information',
  BUSINESS_INFORMATION = 'business_information',
  FINANCIAL_ADVISORS = 'financial_advisors',
  BOARD_OF_DIRECTORS = 'board_of_directors',
  OTHER_ADVISORS = 'other_advisors',
  DOCUMENTS = 'documents',
  BANKERS = 'bankers',
  BANKING_INFORMATION = 'banking_information',
  LOANS_INFORMATION = 'loans_information',
  PERSONAL_GUARANTEES = 'personal_guarantees',
  LEASES = 'leases',
  INSURANCES = 'insurances',
  MISCELLANEOUS = 'miscellaneous',
  MESSAGE_WISHES = 'message_wishes',
  SHOULD_PRIVATE = 'should_private',
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  UNDER_RENEWAL = 'under_renewal',
}

export enum FinancialGapAnalysisScreens {
  GET_STARTED = 'get_started',
  INVESTMENTS = 'investments',
  POST_EXIT_FINANCES = 'post_exit_finances',
  BUSINESS_SALE_COSTS = 'business_sale_costs',
}

export enum FinancialGapAssessmentCalculatorTabs {
  HOUSEHOLD_EXPENSES = 'household_expenses',
  FOOD_AND_GROCERY = 'food_and_grocery',
  PERSONAL_CARE = 'personal_care',
  HEALTH_CARE = 'health_care',
  TRANSPORTATION = 'Transportation',
  LOANSPAYMENTS = 'loanspayments',
  PROFESSIONAL = 'professional',
  MISCELLANEOUS = 'miscellaneous',
  DISCRETIONARY = 'discretionary',
  EXPENSES = 'expenses',
}

export enum OwnerType {
  SINGLE_OWNER = 'single_owner',
  MULTIPLE_OWNER = 'multiple_owner',
}
